#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import mysql.connector

def test_mysql_connection():
    """اختبار اتصال MySQL بطرق مختلفة"""
    
    # قائمة بالمحاولات المختلفة
    attempts = [
        {"user": "root", "password": "", "desc": "بدون كلمة مرور"},
        {"user": "root", "password": "800900@@", "desc": "بكلمة المرور 800900@@"},
        {"user": "Tommy", "password": "800900@@", "desc": "بالمستخدم Tommy"},
        {"user": "root", "password": "root", "desc": "بكلمة المرور root"},
        {"user": "root", "password": "password", "desc": "بكلمة المرور password"},
    ]
    
    for attempt in attempts:
        try:
            print(f"محاولة الاتصال {attempt['desc']}...")
            conn = mysql.connector.connect(
                host='localhost',
                user=attempt['user'],
                password=attempt['password'],
                port=3306
            )
            print(f"✅ نجح الاتصال {attempt['desc']}")
            
            # اختبار إنشاء قاعدة بيانات
            cursor = conn.cursor()
            cursor.execute("SHOW DATABASES")
            databases = cursor.fetchall()
            print(f"قواعد البيانات الموجودة: {len(databases)}")
            
            cursor.close()
            conn.close()
            return attempt  # إرجاع الإعدادات الناجحة
            
        except Exception as e:
            print(f"❌ فشل {attempt['desc']}: {str(e)}")
    
    return None

if __name__ == "__main__":
    print("=== اختبار اتصال MySQL ===")
    successful_config = test_mysql_connection()
    
    if successful_config:
        print(f"\n🎉 تم العثور على إعدادات صحيحة:")
        print(f"المستخدم: {successful_config['user']}")
        print(f"كلمة المرور: '{successful_config['password']}'")
    else:
        print("\n❌ فشل في جميع المحاولات")
        print("يرجى التحقق من إعدادات MySQL")
