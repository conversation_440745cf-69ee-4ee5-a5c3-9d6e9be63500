#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محول قاعدة البيانات من MySQL إلى SQLite
نسخة مبسطة لتشغيل النظام بدون مشاكل MySQL
"""

import sqlite3
import pickle
import os
from datetime import date

# مسار قاعدة البيانات
DB_PATH = "tiktok.db"
current_date = date.today()

def startDatabase():
    """بدء قاعدة البيانات وإنشاء الجداول"""
    initDatabase()

def initDatabase():
    """إنشاء قاعدة البيانات والجداول المطلوبة"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # إنشاء جدول المقاطع
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS clip_bin (
            clip_num INTEGER PRIMARY KEY AUTOINCREMENT,
            clip_id TEXT,
            date TEXT,
            status TEXT,
            clipwrapper BLOB,
            filter_name TEXT
        )
    ''')
    
    # إنشاء جدول الفلاتر
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS filters (
            num INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT,
            filterwrapper BLOB
        )
    ''')
    
    conn.commit()
    conn.close()
    print("تم إنشاء قاعدة البيانات SQLite بنجاح")

def addFoundClip(tiktokclip, filterName):
    """إضافة مقطع جديد تم العثور عليه"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    clip_id = tiktokclip.id
    clipblob = pickle.dumps(tiktokclip)
    
    cursor.execute('''
        INSERT INTO clip_bin (clip_id, date, filter_name, status, clipwrapper)
        VALUES (?, ?, ?, 'FOUND', ?)
    ''', (clip_id, str(current_date), filterName, clipblob))
    
    conn.commit()
    conn.close()

def updateStatusWithClip(clip_id, status, clip):
    """تحديث حالة المقطع"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    clipblob = pickle.dumps(clip)
    
    cursor.execute('''
        UPDATE clip_bin 
        SET status = ?, clipwrapper = ?
        WHERE clip_id = ?
    ''', (status, clipblob, clip_id))
    
    conn.commit()
    conn.close()

def addFilter(filter_name, filterobject):
    """إضافة فلتر جديد"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    filterobjectdumped = pickle.dumps(filterobject)
    
    cursor.execute('''
        INSERT INTO filters (name, filterwrapper)
        VALUES (?, ?)
    ''', (filter_name, filterobjectdumped))
    
    conn.commit()
    conn.close()

def getAllSavedFilters():
    """الحصول على جميع الفلاتر المحفوظة"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('SELECT name FROM filters')
    filters = cursor.fetchall()
    
    conn.close()
    return filters

def getSavedFilterByName(filter_name):
    """الحصول على فلتر بالاسم"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('SELECT filterwrapper FROM filters WHERE name = ?', (filter_name,))
    result = cursor.fetchone()
    
    conn.close()
    
    if result:
        return pickle.loads(result[0])
    return None

def getAllSavedClipIDs():
    """الحصول على جميع معرفات المقاطع المحفوظة"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('SELECT clip_id FROM clip_bin')
    clip_ids = cursor.fetchall()
    
    conn.close()
    return clip_ids

def getFilterClipsByStatusLimit(filter_name, status, limit):
    """الحصول على مقاطع فلتر معين بحالة محددة"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT clipwrapper FROM clip_bin 
        WHERE filter_name = ? AND status = ?
        LIMIT ?
    ''', (filter_name, status, limit))
    
    results = cursor.fetchall()
    conn.close()
    
    clips = []
    for result in results:
        try:
            clip = pickle.loads(result[0])
            clips.append(clip)
        except:
            continue
    
    return clips

def getFoundClips(filter_name, limit):
    """الحصول على المقاطع التي تم العثور عليها"""
    return getFilterClipsByStatusLimit(filter_name, "FOUND", limit)

def getFilterNames():
    """الحصول على أسماء جميع الفلاتر"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()

    cursor.execute('SELECT name FROM filters')
    filters = cursor.fetchall()

    conn.close()
    return [filter[0] for filter in filters]

# إضافة وظائف أخرى حسب الحاجة...
def getClipsWithoutIds(filter_name, amount, excluded_ids):
    """الحصول على مقاطع بدون معرفات محددة"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # تحويل قائمة المعرفات المستبعدة إلى نص للاستعلام
    placeholders = ','.join(['?' for _ in excluded_ids])
    query = f'''
        SELECT clipwrapper FROM clip_bin 
        WHERE filter_name = ? AND status = 'DOWNLOADED' 
        AND clip_id NOT IN ({placeholders})
        LIMIT ?
    '''
    
    params = [filter_name] + excluded_ids + [amount]
    cursor.execute(query, params)
    
    results = cursor.fetchall()
    conn.close()
    
    clips = []
    for result in results:
        try:
            clip = pickle.loads(result[0])
            clips.append(clip)
        except:
            continue
    
    return clips
